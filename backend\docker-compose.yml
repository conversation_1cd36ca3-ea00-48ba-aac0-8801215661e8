version: '3.9'

services: 
  mongodb:
    image: mongo:latest
    container_name: mshare
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: MyPassword
      MONGO_INITDB_DATABASE: mshare
    ports:
      - "27017:27017"

  backend:
    build: .
    container_name: backend
    restart: always
    depends_on:
      - mongodb
    environment:
      MONGODB_URI: ***************************************************************
    ports:
      - "3000:3000"
