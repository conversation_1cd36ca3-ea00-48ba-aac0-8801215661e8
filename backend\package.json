{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@aws-sdk/client-s3": "^3.872.0", "@aws-sdk/s3-request-presigner": "^3.872.0", "@types/cors": "^2.8.19", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "mongoose": "^8.17.2", "multer": "^2.0.2", "node-cron": "^4.2.1", "uuid": "^11.1.0", "zod": "^4.0.17"}, "devDependencies": {"@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "^24.3.0", "@types/node-cron": "^3.0.11", "nodemon": "^3.1.10", "ts-node": "^10.9.2"}}