import { config } from "dotenv";

const configFile = `./.env`;
config({ path: configFile});

const { 
    AWS_REGION, 
    S3_BUCKET_NAME,
    AWS_ACCESS_KEY_ID, 
    AWS_SECRET_ACCESS_KEY, 
    FRONTEND_ORIGIN, 
    PORT, 
    OPTIONS, 
    MONGODB_URI,
    NODE_ENV, 
    } = process.env;

export default { AWS_REGION, 
    S3_BUCKET_NAME,
    AWS_ACCESS_KEY_ID, 
    AWS_SECRET_ACCESS_KEY, 
    FRONTEND_ORIGIN, 
    PORT, 
    OPTIONS, 
    MONGODB_URI,
    env : NODE_ENV
}

