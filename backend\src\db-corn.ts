import cron from "node-cron";
import { File } from "./db";

const deleteDBdata = async () => {
    try {
        const now = new Date();
        const result = await File.deleteMany({
            expiresAt: { $lt: now },
        });

        if (result.deletedCount && result.deletedCount > 0) {
            console.log(`🗑️ Cleanup: Deleted ${result.deletedCount} expired file records at ${now.toISOString()}`);
        } else {
            console.log(`⏳ Cleanup ran at ${now.toISOString()} - no expired files.`);
        }

    } catch (err) {
        console.error("❌ Error in cleanup job:", err);
    }
}

// Schedule a cron job to run every 10 minutes
export const scheduleCleanup = () => {
    console.log("Cron job scheduled to run every 10 minutes");
    cron.schedule("*/10 * * * *", deleteDBdata);
    // Run once on startup
    deleteDBdata();
}