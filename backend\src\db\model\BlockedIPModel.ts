import mongoose from "mongoose";

export interface IBlockedIP extends mongoose.Document {
  ip: string;
  reason?: string;
  blockedAt: Date;
  permanent: boolean;
}

const BlockedIPSchema = new mongoose.Schema({
  ip: { type: String, required: true, unique: true },
  reason: { type: String, default: "Rate limit abuse" },
  blockedAt: { type: Date, default: Date.now },
  permanent: { type: Boolean, default: false },
});

export const BlockedIP = mongoose.model<IBlockedIP>("BlockedIP", BlockedIPSchema);