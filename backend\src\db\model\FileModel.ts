import mongoose from "mongoose";

const fileSchema = new mongoose.Schema({
  fileId: {
    type: String,
    required: true,
    unique: true, // uuid we generate
  },
  originalName: {
    type: String,
    required: true,
  },
  s3Key: {
    type: String,
    required: true,
  },
  mime: {
    type: String,
    required: true,
  },
  fileType: {
    type: String,
    enum: ["image", "video", "audio", "file"],
    default: "file",
  },
  size: {
    type: Number, // in bytes
    required: true,
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
  // optional fields for tracking
  downloads: {
    type: Number,
    default: 0,
  },
  expiresAt: {
    type: Date, // if you want auto-delete after X days
  },
});

const File = mongoose.model("File", fileSchema);

export default File;
