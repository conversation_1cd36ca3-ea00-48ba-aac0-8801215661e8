// ---------- Rate limiting with IP Blocking ---------

import rateLimit from "express-rate-limit";
import { Request, Response, NextFunction } from "express";
import { BlockedIP } from "../db";

export const ipBlockMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip;

    try {
        const blocked = await BlockedIP.findOne({ ip: clientIP ,
            permanent : true
        })

        if (blocked) {
            console.log(`[BLOCKED] IP ${clientIP} tried to access ${req.method} ${req.path}`);
            return res.status(403).json({
                error: "Access Forbidden",
                message: "You have been permanently blocked due to security violations.",
            });
        }
    } catch (err) {
         console.error("MongoDB lookup failed", err);
    }

    next();
}

// ---------- Helper to add to blocklist ----------
async function permanentlyBlockIP(ip: string, reason = "Rate limit abuse") {
  try {
    await BlockedIP.findOneAndUpdate(
      { ip },
      { ip, reason, blockedAt: new Date(), permanent: true },
      { upsert: true }
    );
    console.log(`[PERMANENT BLOCK] IP ${ip} added to database.`);
  } catch (err) {
    console.error("Error blocking IP:", err);
  }
}

// ---------- Rate Limiters ----------
function createRateLimiter(max: number, windowMs: number, name: string) {
    return rateLimit({
        windowMs,
        max,
        standardHeaders: true,
        legacyHeaders: false,
        handler: async (req, res) => {
            const clientIP = req.ip;
            console.warn(`[RATE LIMIT HIT] ${name} - IP: ${clientIP}`);

            // Permanently block the IP in MongoDB
            if (clientIP) await permanentlyBlockIP(clientIP, `Abused ${name} limiter`);

             res.status(429).json({
                error: "Too many requests",
                message: `You have been permanently blocked due to repeated abuse.`,
            });
        },
    });
}

export const sendRateLimiter = createRateLimiter(10, 15 * 60 * 1000, "SEND"); // 10 requests/15min
export const receiveRateLimiter = createRateLimiter(30, 15 * 60 * 1000, "RECEIVE"); // 30 requests/15min
export const generalRateLimiter = createRateLimiter(100, 15 * 60 * 1000, "GENERAL"); // 100 requests/15min


// // Rate limiter for send endpoint (more restrictive)
// export const sendRateLimiter = rateLimit({
//     windowMs: 15 * 60 * 1000, // 15 minutes
//     max: 10, // limit each IP to 10 requests per windowMs
//     standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
//     legacyHeaders: false, // Disable the `X-RateLimit-*` headers
//     handler: (req, res) => {
//         // Calculate time until the next request can be made
//         // This is the time until the oldest request in the window expires
//         const retryAfter = Math.ceil(15 * 60 / 10); // 90 seconds - time until next slot opens
//         res.set('Retry-After', retryAfter.toString());
//         res.status(429).json({
//             error: "Too many requests",
//             message: `Rate limit hit. Try again in ${retryAfter} seconds.`,
//             retryAfter: retryAfter
//         });
//     }
// });