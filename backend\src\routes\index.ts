import { Request, Response, Router } from "express";
import { sendRateLimiter, receiveRateLimiter } from "../middlewares/security";
import config from "../config/config";
import { S3Client, PutObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import multer from "multer";
import { v4 as uuidv4 } from "uuid";
import { File } from "../db";

const router = Router();
const s3 = new S3Client({ region: config.AWS_REGION });

const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 1024 * 1024 * 1024 }, // 1GB
});

function detectFileType(mime: string) {
  if (mime.startsWith("image/")) return "image";
  if (mime.startsWith("video/")) return "video";
  if (mime.startsWith("audio/")) return "audio";
  return "file";
}

// UPLOAD FILE
router.post("/upload", sendRateLimiter, upload.single("file"), async (req: Request, res: Response) => {
    try {
      const file = req.file;
      if (!file) return res.status(400).send("No file uploaded.");

      const id = uuidv4();
      const fileType = detectFileType(file.mimetype);
      const s3Key = `${id}-${Date.now()}`;

      // Upload to S3
      await s3.send(
        new PutObjectCommand({
          Bucket: config.S3_BUCKET_NAME,
          Key: s3Key,
          Body: file.buffer,
          ContentType: file.mimetype,
        })
      );

      // Save in MongoDB
      await File.create({
        fileId: id,
        originalName: file.originalname,
        s3Key,
        mime: file.mimetype,
        fileType,
        size: file.size,
      });

      // Shareable URL
      const shareUrl = `${config.FRONTEND_ORIGIN}/download/${id}`;

      res.json({
        success: true,
        message: "File uploaded!",
        shareUrl,
      });
    } catch (err) {
      console.error(err);
      res.status(500).send("Error uploading file.");
    }
  }
);

// GET FILE METADATA
router.get("/file/:id", receiveRateLimiter, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const file = await File.findOne({ fileId: id });

    if (!file) return res.status(404).send("File not found.");

    res.json({
      success: true,
      file: {
        name: file.originalName,
        type: file.fileType,
        mime: file.mime,
        size: file.size,
      },
    });
  } catch (err) {
    console.error(err);
    res.status(500).send("Error fetching file metadata.");
  }
});

// DOWNLOAD FILE 
router.get("/download/:id", receiveRateLimiter, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const file = await File.findOne({ fileId: id });
    if (!file) return res.status(404).send("File not found.");

    // Generate signed URL
    const signedUrl = await getSignedUrl(
      s3,
      new GetObjectCommand({
        Bucket: config.S3_BUCKET_NAME,
        Key: file.s3Key,
      }),
      { expiresIn: 60 * 5 } // 5 minutes
    );

    // Redirect → browser starts download
    res.redirect(signedUrl);
  } catch (err) {
    console.error(err);
    res.status(500).send("Error generating download link.");
  }
});

export default router;
