import express from "express";
import cors from "cors";
import config from "./config/config";
import { generalRateLimiter, ipBlockMiddleware } from "./middlewares/security";
import router from "./routes";
import { connectDB } from "./db";
import  { scheduleCleanup } from"./db-corn";
import { scheduleWarmup } from "./warmup-cron";

const app = express();

app.use(cors());
app.use(express.json());
app.use(ipBlockMiddleware);

app.use(generalRateLimiter);
app.use("/",router)

const port  = config.PORT || 3000;

async function startServer() {
    try {
        await connectDB();
        app.listen(port, () => {
            console.log(`Server listening on port ${port}`);
            scheduleCleanup();
            scheduleWarmup();
        });
    } catch (err) {
        console.error(err);
        process.exit(1);
    }
}

startServer();