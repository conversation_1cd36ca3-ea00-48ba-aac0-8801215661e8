import cron from 'node-cron';
import axios from 'axios';

const PORT = process.env.PORT || 3000;
const BACKEND_URL = `http://localhost:${PORT}`;

const warmup = async () => {    
    try {
        await axios.get(BACKEND_URL);
        console.log('Warmup successful');
    } catch (err) {
        console.error('Warmup failed:', err);
    }
}

export const scheduleWarmup = () => {
    console.log("Warmup job scheduled to run every 20 minutes");
    cron.schedule("*/20 * * * *", warmup);
    // Run once on startup
    warmup();
}