import { useState } from "react";

function App() {
  const [tab, setTab] = useState("upload");
  const [dragActive, setDragActive] = useState(false);
  const [file, setFile] = useState<File | null>(null);

  const [url, setUrl] = useState("");
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [metadata, setMetadata] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // ---- Upload Handlers ----
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = (e: React.FormEvent) => {
    e.preventDefault();
    if (file) {
      console.log("Uploading file:", file.name);
      // TODO: send file to backend (S3 upload API)
    }
  };

  // ---- Download Handlers ----
  const handleFetchMetadata = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url) return;
    try {
      setLoading(true);
      setMetadata(null);

      // Call backend to get metadata
      const res = await fetch(`/api/file/metadata?url=${encodeURIComponent(url)}`);
      const data = await res.json();
      setMetadata(data);
    } catch (err) {
      console.error("Error fetching metadata:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex justify-center items-center h-screen bg-gray-100">
      <div className="bg-white shadow-xl rounded-2xl p-8 w-[500px]">
        {/* Tabs */}
        <div className="flex mb-6 border-b">
          <button
            onClick={() => setTab("upload")}
            className={`flex-1 py-2 font-medium text-center ${
              tab === "upload"
                ? "text-blue-600 border-b-2 border-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Upload
          </button>
          <button
            onClick={() => setTab("download")}
            className={`flex-1 py-2 font-medium text-center ${
              tab === "download"
                ? "text-blue-600 border-b-2 border-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Download
          </button>
        </div>

        {/* Upload Form */}
        {tab === "upload" && (
          <form className="space-y-4" onSubmit={handleUpload}>
            <div
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              className={`w-full h-32 border-2 border-dashed rounded-lg flex items-center justify-center cursor-pointer transition ${
                dragActive
                  ? "border-blue-600 bg-blue-50"
                  : "border-gray-300 bg-gray-50"
              }`}
            >
              {file ? (
                <p className="text-gray-700 text-sm">{file.name}</p>
              ) : (
                <p className="text-gray-500 text-sm">
                  Drag & Drop your file here or{" "}
                  <label className="text-blue-600 cursor-pointer">
                    browse
                    <input
                      type="file"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                  </label>
                </p>
              )}
            </div>

            <button
              type="submit"
              disabled={!file}
              className="w-full bg-blue-600 text-white py-2 rounded-lg 
                hover:bg-blue-700 disabled:bg-gray-300 transition"
            >
              {file ? "Upload File" : "Select a File First"}
            </button>
          </form>
        )}

        {/* Download Form */}
        {tab === "download" && (
          <div className="space-y-4">
            <form onSubmit={handleFetchMetadata} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Paste URL
                </label>
                <input
                  type="text"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="Enter download URL"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-green-600 text-white py-2 rounded-lg 
                  hover:bg-green-700 transition"
              >
                {loading ? "Fetching..." : "Get Metadata"}
              </button>
            </form>

            {/* File Preview */}
            {metadata && (
              <div className="mt-4 border rounded-lg p-4 bg-gray-50">
                <p className="font-medium text-gray-700">
                  {metadata.originalName} ({(metadata.size / 1024).toFixed(2)} KB)
                </p>
                <p className="text-sm text-gray-500">Type: {metadata.mime}</p>

                <div className="mt-3">
                  {metadata.fileType === "image" && (
                    <img src={url} alt={metadata.originalName} className="max-h-48 rounded-lg" />
                  )}
                  {metadata.fileType === "video" && (
                    <video controls className="w-full rounded-lg">
                      <source src={url} type={metadata.mime} />
                      Your browser does not support the video tag.
                    </video>
                  )}
                  {metadata.fileType === "audio" && (
                    <audio controls className="w-full">
                      <source src={url} type={metadata.mime} />
                      Your browser does not support the audio tag.
                    </audio>
                  )}
                </div>

                <a
                  href={url}
                  download={metadata.originalName}
                  className="mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Download
                </a>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
